// Smart AdBlocker Popup Script
console.log('Smart AdBlocker popup loaded');

let currentHostname = '';
let currentSettings = {};

// DOM yüklendiğinde başlat
document.addEventListener('DOMContentLoaded', initialize);

async function initialize() {
  console.log('Initializing popup');
  
  // Current tab bilgisini al
  await getCurrentTab();
  
  // Ayarları yükle
  await loadSettings();
  
  // UI'ı güncelle
  updateUI();
  
  // Event listener'ları ekle
  setupEventListeners();
}

// Aktif tab bilgisini al
async function getCurrentTab() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tab && tab.url) {
      const url = new URL(tab.url);
      currentHostname = url.hostname;
      console.log('Current hostname:', currentHostname);
    }
  } catch (error) {
    console.error('Error getting current tab:', error);
  }
}

// <PERSON>yarlar<PERSON> yükle
async function loadSettings() {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'getSettings' });
    currentSettings = response.settings || {};
    console.log('Settings loaded:', currentSettings);
  } catch (error) {
    console.error('Error loading settings:', error);
    currentSettings = {
      isEnabled: true,
      whitelist: [],
      blockAds: true,
      blockVideoAds: true,
      blockPopups: true,
      blockTrackers: true
    };
  }
}

// UI'ı güncelle
function updateUI() {
  // Status güncelle
  updateStatus();
  
  // Site bilgisini güncelle
  updateSiteInfo();
  
  // Toggle'ları güncelle
  updateToggles();
  
  // Whitelist'i güncelle
  updateWhitelist();
  
  // Stats'ları güncelle (şimdilik mock data)
  updateStats();
}

// Status indicator'ı güncelle
function updateStatus() {
  const statusDot = document.getElementById('statusDot');
  const statusText = document.getElementById('statusText');
  
  if (currentSettings.isEnabled) {
    statusDot.classList.remove('inactive');
    statusText.textContent = 'Active';
  } else {
    statusDot.classList.add('inactive');
    statusText.textContent = 'Disabled';
  }
}

// Site bilgisini güncelle
function updateSiteInfo() {
  const siteName = document.getElementById('siteName');
  const siteStatus = document.getElementById('siteStatus');
  const whitelistBtn = document.getElementById('whitelistBtn');
  const whitelistIcon = document.getElementById('whitelistIcon');
  const whitelistText = document.getElementById('whitelistText');
  
  // Site adını göster
  siteName.textContent = currentHostname || 'Unknown Site';
  
  // Whitelist durumunu kontrol et
  const isWhitelisted = currentSettings.whitelist && 
                       currentSettings.whitelist.includes(currentHostname);
  
  if (isWhitelisted) {
    siteStatus.textContent = 'Ads Allowed';
    whitelistBtn.classList.add('whitelisted');
    whitelistIcon.textContent = '✗';
    whitelistText.textContent = 'Remove from whitelist';
  } else {
    siteStatus.textContent = 'Ads Blocked';
    whitelistBtn.classList.remove('whitelisted');
    whitelistIcon.textContent = '✓';
    whitelistText.textContent = 'Allow for this site';
  }
}

// Toggle'ları güncelle
function updateToggles() {
  document.getElementById('mainToggle').checked = currentSettings.isEnabled || false;
  document.getElementById('blockAdsToggle').checked = currentSettings.blockAds || false;
  document.getElementById('blockVideoAdsToggle').checked = currentSettings.blockVideoAds || false;
  document.getElementById('blockPopupsToggle').checked = currentSettings.blockPopups || false;
  document.getElementById('blockTrackersToggle').checked = currentSettings.blockTrackers || false;
}

// Whitelist'i güncelle
function updateWhitelist() {
  const whitelistList = document.getElementById('whitelistList');
  const whitelistCount = document.getElementById('whitelistCount');
  const whitelist = currentSettings.whitelist || [];
  
  // Count'u güncelle
  whitelistCount.textContent = whitelist.length;
  
  // Liste'yi temizle
  whitelistList.innerHTML = '';
  
  // Whitelist item'larını ekle
  whitelist.forEach(site => {
    const item = createWhitelistItem(site);
    whitelistList.appendChild(item);
  });
  
  // Eğer liste boşsa placeholder göster
  if (whitelist.length === 0) {
    const placeholder = document.createElement('div');
    placeholder.textContent = 'No whitelisted sites';
    placeholder.style.color = '#999';
    placeholder.style.fontSize = '12px';
    placeholder.style.textAlign = 'center';
    placeholder.style.padding = '8px';
    whitelistList.appendChild(placeholder);
  }
}

// Whitelist item oluştur
function createWhitelistItem(site) {
  const item = document.createElement('div');
  item.className = 'whitelist-item';
  
  const siteSpan = document.createElement('span');
  siteSpan.className = 'whitelist-site';
  siteSpan.textContent = site;
  
  const removeBtn = document.createElement('button');
  removeBtn.className = 'remove-btn';
  removeBtn.textContent = '✗';
  removeBtn.title = `Remove ${site} from whitelist`;
  removeBtn.addEventListener('click', () => removeSiteFromWhitelist(site));
  
  item.appendChild(siteSpan);
  item.appendChild(removeBtn);
  
  return item;
}

// Stats'ları güncelle (mock data)
function updateStats() {
  // Bu veriler gerçek implementasyonda storage'dan gelecek
  document.getElementById('adsBlocked').textContent = '127';
  document.getElementById('trackersBlocked').textContent = '89';
}

// Event listener'ları ekle
function setupEventListeners() {
  // Main toggle
  document.getElementById('mainToggle').addEventListener('change', async (e) => {
    currentSettings.isEnabled = e.target.checked;
    await saveSettings();
    updateStatus();
  });
  
  // Whitelist button
  document.getElementById('whitelistBtn').addEventListener('click', toggleCurrentSiteWhitelist);
  
  // Setting toggles
  document.getElementById('blockAdsToggle').addEventListener('change', async (e) => {
    currentSettings.blockAds = e.target.checked;
    await saveSettings();
  });
  
  document.getElementById('blockVideoAdsToggle').addEventListener('change', async (e) => {
    currentSettings.blockVideoAds = e.target.checked;
    await saveSettings();
  });
  
  document.getElementById('blockPopupsToggle').addEventListener('change', async (e) => {
    currentSettings.blockPopups = e.target.checked;
    await saveSettings();
  });
  
  document.getElementById('blockTrackersToggle').addEventListener('change', async (e) => {
    currentSettings.blockTrackers = e.target.checked;
    await saveSettings();
  });
  
  // Settings button
  document.getElementById('settingsBtn').addEventListener('click', () => {
    chrome.runtime.openOptionsPage();
  });
}

// Current site'ı whitelist'te toggle et
async function toggleCurrentSiteWhitelist() {
  if (!currentHostname) {
    console.error('No current hostname');
    return;
  }
  
  try {
    await chrome.runtime.sendMessage({ 
      action: 'toggleSite', 
      hostname: currentHostname 
    });
    
    // Ayarları yeniden yükle ve UI'ı güncelle
    await loadSettings();
    updateSiteInfo();
    updateWhitelist();
    
    console.log(`Toggled whitelist for ${currentHostname}`);
  } catch (error) {
    console.error('Error toggling site whitelist:', error);
  }
}

// Site'ı whitelist'ten kaldır
async function removeSiteFromWhitelist(site) {
  try {
    await chrome.runtime.sendMessage({ 
      action: 'toggleSite', 
      hostname: site 
    });
    
    // Ayarları yeniden yükle ve UI'ı güncelle
    await loadSettings();
    updateSiteInfo();
    updateWhitelist();
    
    console.log(`Removed ${site} from whitelist`);
  } catch (error) {
    console.error('Error removing site from whitelist:', error);
  }
}

// Ayarları kaydet
async function saveSettings() {
  try {
    await chrome.runtime.sendMessage({ 
      action: 'updateSettings', 
      settings: currentSettings 
    });
    console.log('Settings saved:', currentSettings);
  } catch (error) {
    console.error('Error saving settings:', error);
  }
}

// Error handling
window.addEventListener('error', (e) => {
  console.error('Popup error:', e.error);
});

// Unhandled promise rejections
window.addEventListener('unhandledrejection', (e) => {
  console.error('Unhandled promise rejection:', e.reason);
});