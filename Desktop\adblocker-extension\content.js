// Content Script - Smart AdBlocker
console.log('Smart AdBlocker content script loaded');

let isEnabled = true;
let isWhitelisted = false;
let settings = {};

// <PERSON><PERSON> yüklendiğinde başlat
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}

// Ana başlatma fonksiyonu
async function initialize() {
  console.log('Initializing Smart AdBlocker');
  
  // <PERSON>yarları yükle
  await loadSettings();
  
  // Site whitelist kontrolü
  await checkSiteWhitelist();
  
  if (!isWhitelisted && settings.isEnabled) {
    // YouTube için özel ad blocking
    if (window.location.hostname.includes('youtube.com')) {
      startYouTubeAdBlocking();
    } else {
      startAdBlocking();
    }
  }
}

// Ayarları yükle
async function loadSettings() {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'getSettings' });
    settings = response.settings || {};
    console.log('Settings loaded:', settings);
  } catch (error) {
    console.log('Error loading settings:', error);
  }
}

// Site whitelist kontrolü
async function checkSiteWhitelist() {
  const hostname = window.location.hostname;
  try {
    const response = await chrome.runtime.sendMessage({ 
      action: 'checkWhitelist', 
      hostname: hostname 
    });
    isWhitelisted = response.isWhitelisted;
    console.log(`Site ${hostname} whitelisted:`, isWhitelisted);
  } catch (error) {
    console.log('Error checking whitelist:', error);
  }
}

// Ad blocking'i başlat
function startAdBlocking() {
  console.log('Starting ad blocking');
  
  // CSS ile reklam elementlerini gizle
  injectAdBlockCSS();
  
  // YouTube dışında DOM manipulation yap
  if (!window.location.hostname.includes('youtube.com')) {
    // DOM'da reklam elementlerini bul ve kaldır
    removeAdElements();
  }
  
  // Video reklamlarını blokla
  if (settings.blockVideoAds) {
    blockVideoAds();
  }
  
  // Popup'ları blokla
  if (settings.blockPopups) {
    blockPopups();
  }
  
  // DOM değişikliklerini izle
  observeDOM();
}

// CSS ile reklam elementlerini gizle
function injectAdBlockCSS() {
  // YouTube için özel CSS
  if (window.location.hostname.includes('youtube.com')) {
    const youtubeCSS = `
      /* Sadece YouTube reklamları */
      .video-ads, .ytp-ad-module, .ytp-ad-overlay-container,
      [class*="ad-showing"], [class*="ad-interrupting"],
      ytd-promoted-sparkles-web-renderer,
      ytd-ad-slot-renderer, ytd-promoted-video-renderer {
        display: none !important;
      }
    `;
    
    const styleSheet = document.createElement('style');
    styleSheet.type = 'text/css';
    styleSheet.innerHTML = youtubeCSS;
    styleSheet.id = 'smart-adblocker-youtube-css';
    
    const target = document.head || document.body || document.documentElement;
    if (target) {
      target.appendChild(styleSheet);
    }
    return;
  }
  
  // Diğer siteler için genel CSS
  const adBlockCSS = `
    /* Genel reklam selektörleri - daha konservatif */
    iframe[src*="googlesyndication"],
    iframe[src*="doubleclick"],
    iframe[src*="googleadservices"],
    div[data-ad-client],
    
    /* Açık reklam class'ları */
    .advertisement, .banner-ad, .popup-ad,
    
    /* Cookie banner'ları */
    [class*="cookie-consent"], [class*="cookie-banner"],
    [id*="cookie-consent"], [id*="cookie-banner"],
    
    /* Newsletter popup'ları */
    [class*="newsletter-popup"], [class*="subscribe-popup"],
    [class*="email-popup"] {
      display: none !important;
    }
    
    /* Body scroll'u geri getir */
    body {
      overflow: auto !important;
    }
  `;
  
  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerHTML = adBlockCSS;
  styleSheet.id = 'smart-adblocker-css';
  
  // Head varsa oraya, yoksa body'ye ekle
  const target = document.head || document.body || document.documentElement;
  if (target) {
    target.appendChild(styleSheet);
  }
}

// DOM'dan reklam elementlerini kaldır
function removeAdElements() {
  // YouTube'da DOM manipulation yapma
  if (window.location.hostname.includes('youtube.com')) {
    return;
  }
  
  // Yaygın reklam selektörleri - daha konservatif
  const adSelectors = [
    'iframe[src*="googlesyndication"]',
    'iframe[src*="doubleclick"]',
    'div[data-ad-client]',
    '.advertisement', '.banner-ad',
    '.cookie-consent', '.cookie-banner',
    '.newsletter-popup'
  ];
  
  adSelectors.forEach(selector => {
    try {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        // Önemli content'i silmemek için kontrol
        if (!isImportantElement(element)) {
          element.remove();
        }
      });
    } catch (error) {
      // Selector hatası, devam et
    }
  });
}

// Önemli element kontrolü
function isImportantElement(element) {
  const importantTags = ['nav', 'header', 'main', 'article', 'section'];
  const importantClasses = ['content', 'main', 'primary', 'article', 'post'];
  
  // Tag kontrol
  if (importantTags.includes(element.tagName.toLowerCase())) {
    return true;
  }
  
  // Class kontrol
  const className = element.className.toLowerCase();
  return importantClasses.some(cls => className.includes(cls));
}

// Video reklamlarını blokla
function blockVideoAds() {
  // YouTube için çok konservatif yaklaşım
  if (window.location.hostname.includes('youtube.com')) {
    // Sadece açık reklam container'larını gizle
    const ytAdSelectors = [
      '.video-ads', '.ytp-ad-module', '.ytp-ad-overlay-container'
    ];
    
    ytAdSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(el => el.style.display = 'none');
    });
    return;
  }
  
  // Diğer siteler için video reklam engelleme
  const videoAdSelectors = [
    'video[src*="googlesyndication"]',
    'video[src*="doubleclick"]',
    'video[src*="/ads/"]'
  ];
  
  videoAdSelectors.forEach(selector => {
    const videos = document.querySelectorAll(selector);
    videos.forEach(video => {
      video.style.display = 'none';
    });
  });
}

// Popup'ları blokla
function blockPopups() {
  // Modal ve overlay'leri kaldır
  const popupSelectors = [
    '.modal', '.popup', '.overlay', '.backdrop',
    '[role="dialog"]', '[role="alertdialog"]'
  ];
  
  popupSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      if (element.innerHTML.toLowerCase().includes('ad') || 
          element.innerHTML.toLowerCase().includes('subscribe') ||
          element.innerHTML.toLowerCase().includes('newsletter')) {
        element.remove();
      }
    });
  });
  
  // Body scroll'u geri getir
  document.body.style.overflow = 'auto';
  document.documentElement.style.overflow = 'auto';
}

// DOM değişikliklerini izle
function observeDOM() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Yeni eklenen elementleri kontrol et
            checkNewElement(node);
          }
        });
      }
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

// Yeni element kontrolü
function checkNewElement(element) {
  // Reklam benzeri elementleri kaldır
  const adKeywords = ['ad', 'advertisement', 'banner', 'popup', 'sponsored'];
  const elementClass = (element.className || '').toLowerCase();
  const elementId = (element.id || '').toLowerCase();
  
  if (adKeywords.some(keyword => 
    elementClass.includes(keyword) || elementId.includes(keyword)
  )) {
    if (!isImportantElement(element)) {
      element.remove();
    }
  }
}

// YouTube için özel ad blocking
function startYouTubeAdBlocking() {
  console.log('Starting YouTube-specific ad blocking');

  // Güncel YouTube reklam selektörleri için CSS
  const youtubeAdCSS = `
    /* Video reklamları - 2024 güncel selektörler */
    .video-ads,
    .ytp-ad-module,
    .ytp-ad-overlay-container,
    .ytp-ad-skip-button-container,
    .ytp-ad-overlay-close-button,
    .ytp-ad-image-overlay,
    .ytp-ad-text-overlay,
    .ytp-ad-player-overlay,
    .ytp-ad-player-overlay-instream-info,
    .ytp-ad-overlay-slot,
    .ytp-ad-overlay-image,
    .ad-showing .ytp-ad-module,
    .ad-interrupting .ytp-ad-module,

    /* YouTube promoted content - güncel */
    ytd-promoted-sparkles-web-renderer,
    ytd-ad-slot-renderer,
    ytd-promoted-video-renderer,
    ytd-compact-promoted-video-renderer,
    ytd-promoted-sparkles-text-search-renderer,
    ytd-display-ad-renderer,
    ytd-statement-banner-renderer,
    ytd-banner-promo-renderer,
    ytd-video-masthead-ad-advertiser-info-renderer,
    ytd-video-masthead-ad-primary-video-renderer,

    /* Masthead ads */
    ytd-rich-section-renderer[is-masthead-ad],
    ytd-primetime-promo-renderer,

    /* Display ads */
    #player-ads,
    .ytd-display-ad-renderer,
    #masthead-ad,

    /* Google ads */
    div[id^="google_ads_"],
    div[id^="google_ads_iframe_"],

    /* Sidebar ads */
    ytd-rich-item-renderer[is-ad],

    /* Shorts ads */
    ytd-reel-video-renderer[is-ad],
    ytd-ad-slot-renderer[modern-typography],

    /* Search ads */
    ytd-search-pyv-renderer,
    ytd-promoted-sparkles-text-search-renderer,

    /* Premium prompts */
    ytd-popup-container[dialog-type="premium-upsell"],
    ytd-mealbar-promo-renderer,

    /* Mobile ads */
    .mobile-topbar-header-ad-slot,
    .ytm-promoted-sparkles-web-renderer {
      display: none !important;
      visibility: hidden !important;
      height: 0 !important;
      width: 0 !important;
      opacity: 0 !important;
      pointer-events: none !important;
    }

    /* Video player'da reklam göstergesini gizle */
    .ytp-ad-text,
    .ytp-ad-preview-text,
    .ytp-ad-duration-remaining {
      display: none !important;
    }

    /* Skip button'ı görünür tut */
    .ytp-ad-skip-button,
    .ytp-skip-ad-button {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
    }
  `;

  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerHTML = youtubeAdCSS;
  styleSheet.id = 'smart-adblocker-youtube';

  const target = document.head || document.body || document.documentElement;
  if (target) {
    target.appendChild(styleSheet);
  }

  // Daha agresif reklam temizleme
  setInterval(removeYouTubeAds, 500); // Daha sık kontrol
  setInterval(handleVideoAds, 100); // Video reklamları için çok sık kontrol

  // Sayfa yüklendiğinde hemen kontrol et
  setTimeout(removeYouTubeAds, 100);
  setTimeout(handleVideoAds, 200);

  // DOM observer ile yeni eklenen reklamları yakala
  observeYouTubeAds();
}

// YouTube reklamlarını kaldır - güncellenmiş
function removeYouTubeAds() {
  // Güncel YouTube reklam selektörleri
  const adSelectors = [
    // Video reklamları
    '.video-ads',
    '.ytp-ad-module',
    '.ytp-ad-overlay-container',
    '.ytp-ad-image-overlay',
    '.ytp-ad-text-overlay',
    '.ytp-ad-player-overlay',
    '.ytp-ad-overlay-close-button',
    '.ytp-ad-overlay-slot',
    '.ytp-ad-player-overlay-instream-info',

    // Promoted content
    'ytd-promoted-sparkles-web-renderer',
    'ytd-ad-slot-renderer',
    'ytd-promoted-video-renderer',
    'ytd-compact-promoted-video-renderer',
    'ytd-promoted-sparkles-text-search-renderer',
    'ytd-display-ad-renderer',
    'ytd-statement-banner-renderer',
    'ytd-banner-promo-renderer',
    'ytd-video-masthead-ad-advertiser-info-renderer',
    'ytd-video-masthead-ad-primary-video-renderer',

    // Display ads
    '#player-ads',
    '#masthead-ad',
    '.ytd-display-ad-renderer',
    'div[id^="google_ads_"]',
    'div[id^="google_ads_iframe_"]',

    // Sidebar ve search ads
    'ytd-rich-item-renderer[is-ad]',
    'ytd-search-pyv-renderer',

    // Shorts ads
    'ytd-reel-video-renderer[is-ad]',

    // Masthead
    'ytd-rich-section-renderer[is-masthead-ad]',
    'ytd-primetime-promo-renderer',

    // Premium prompts
    'ytd-popup-container[dialog-type="premium-upsell"]',
    'ytd-mealbar-promo-renderer'
  ];

  // Reklamları kaldır
  adSelectors.forEach(selector => {
    try {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (element && element.parentNode) {
          element.style.display = 'none';
          element.remove();
        }
      });
    } catch (e) {
      // Selector hatası, devam et
    }
  });

  // Reklam içeren parent elementleri kontrol et
  const adContainers = document.querySelectorAll('[class*="ad-"], [id*="ad-"], [class*="ads-"], [id*="ads-"]');
  adContainers.forEach(container => {
    const text = container.textContent.toLowerCase();
    if (text.includes('advertisement') || text.includes('sponsored') || text.includes('reklam')) {
      container.style.display = 'none';
    }
  });
}

// Video reklamlarını özel olarak handle et
function handleVideoAds() {
  const video = document.querySelector('video');
  if (!video) return;

  // Skip button'ları kontrol et ve tıkla
  const skipButtons = document.querySelectorAll(
    '.ytp-ad-skip-button, .ytp-skip-ad-button, .ytp-ad-skip-button-modern, .ytp-ad-skip-button-slot'
  );

  skipButtons.forEach(button => {
    if (button && button.offsetParent !== null && !button.disabled) {
      // Biraz bekle sonra tıkla
      setTimeout(() => {
        try {
          button.click();
          console.log('Skip button clicked');
        } catch (e) {
          // Tıklama hatası
        }
      }, 50);
    }
  });

  // Video player'da reklam kontrolü
  const playerContainer = document.querySelector('.html5-video-player');
  if (playerContainer) {
    // Reklam göstergesi var mı?
    const adIndicators = playerContainer.querySelectorAll(
      '.ytp-ad-text, .ytp-ad-preview-text, .ytp-ad-duration-remaining'
    );

    if (adIndicators.length > 0) {
      // Bu bir reklam videosu
      console.log('Ad detected in video player');

      // Video süresini kontrol et
      if (video.duration && video.duration < 60) {
        // Kısa video (muhtemelen reklam)
        // Hızlandır ve sona yakın götür
        video.playbackRate = 16;
        video.currentTime = Math.max(0, video.duration - 0.5);
        video.muted = true;
      }

      // Skip button yoksa ve video kısaysa sona götür
      if (skipButtons.length === 0 && video.duration && video.duration < 30) {
        video.currentTime = video.duration - 0.1;
      }
    }
  }

  // Reklam overlay'lerini kaldır
  const overlays = document.querySelectorAll(
    '.ytp-ad-overlay-container, .ytp-ad-image-overlay, .ytp-ad-text-overlay'
  );
  overlays.forEach(overlay => {
    overlay.style.display = 'none';
    overlay.remove();
  });
}

// YouTube için DOM observer
function observeYouTubeAds() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Yeni eklenen reklam elementlerini kontrol et
            checkYouTubeAdElement(node);
          }
        });
      }
    });
  });

  // Video player'ı özellikle izle
  const playerContainer = document.querySelector('#movie_player, .html5-video-player');
  if (playerContainer) {
    observer.observe(playerContainer, {
      childList: true,
      subtree: true
    });
  }

  // Genel sayfa değişikliklerini de izle
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

// Yeni YouTube reklam elementi kontrolü
function checkYouTubeAdElement(element) {
  // Reklam class'ları kontrol et
  const adClasses = [
    'ytp-ad-', 'video-ads', 'ytd-promoted-', 'ytd-ad-', 'google_ads_'
  ];

  const elementClass = (element.className || '').toLowerCase();
  const elementId = (element.id || '').toLowerCase();

  if (adClasses.some(adClass =>
    elementClass.includes(adClass) || elementId.includes(adClass)
  )) {
    element.style.display = 'none';
    element.remove();
    console.log('Removed ad element:', element);
  }

  // Alt elementleri de kontrol et
  if (element.querySelectorAll) {
    adClasses.forEach(adClass => {
      const adElements = element.querySelectorAll(`[class*="${adClass}"], [id*="${adClass}"]`);
      adElements.forEach(adEl => {
        adEl.style.display = 'none';
        adEl.remove();
      });
    });
  }
}

// Background script'ten mesaj geldiğinde
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Content script received message:', request);
  
  switch(request.action) {
    case 'updateSettings':
      initialize();
      sendResponse({ success: true });
      break;
  }
});

// Sayfa unload olduğunda temizlik yap
window.addEventListener('beforeunload', () => {
  const adBlockCSS = document.getElementById('smart-adblocker-css');
  if (adBlockCSS) {
    adBlockCSS.remove();
  }
  const youtubeCSS = document.getElementById('smart-adblocker-youtube');
  if (youtubeCSS) {
    youtubeCSS.remove();
  }
});