// Content Script - Smart AdBlocker
console.log('Smart AdBlocker content script loaded');

let isEnabled = true;
let isWhitelisted = false;
let settings = {};

// <PERSON><PERSON> yüklendiğinde başlat
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}

// Ana başlatma fonksiyonu
async function initialize() {
  console.log('Initializing Smart AdBlocker');
  
  // <PERSON>yarları yükle
  await loadSettings();
  
  // Site whitelist kontrolü
  await checkSiteWhitelist();
  
  if (!isWhitelisted && settings.isEnabled) {
    // YouTube için özel ad blocking
    if (window.location.hostname.includes('youtube.com')) {
      startYouTubeAdBlocking();
    } else {
      startAdBlocking();
    }
  }
}

// Ayarları yükle
async function loadSettings() {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'getSettings' });
    settings = response.settings || {};
    console.log('Settings loaded:', settings);
  } catch (error) {
    console.log('Error loading settings:', error);
  }
}

// Site whitelist kontrolü
async function checkSiteWhitelist() {
  const hostname = window.location.hostname;
  try {
    const response = await chrome.runtime.sendMessage({ 
      action: 'checkWhitelist', 
      hostname: hostname 
    });
    isWhitelisted = response.isWhitelisted;
    console.log(`Site ${hostname} whitelisted:`, isWhitelisted);
  } catch (error) {
    console.log('Error checking whitelist:', error);
  }
}

// Ad blocking'i başlat
function startAdBlocking() {
  console.log('Starting ad blocking');
  
  // CSS ile reklam elementlerini gizle
  injectAdBlockCSS();
  
  // YouTube dışında DOM manipulation yap
  if (!window.location.hostname.includes('youtube.com')) {
    // DOM'da reklam elementlerini bul ve kaldır
    removeAdElements();
  }
  
  // Video reklamlarını blokla
  if (settings.blockVideoAds) {
    blockVideoAds();
  }
  
  // Popup'ları blokla
  if (settings.blockPopups) {
    blockPopups();
  }
  
  // DOM değişikliklerini izle
  observeDOM();
}

// CSS ile reklam elementlerini gizle
function injectAdBlockCSS() {
  // YouTube için özel CSS
  if (window.location.hostname.includes('youtube.com')) {
    const youtubeCSS = `
      /* Sadece YouTube reklamları */
      .video-ads, .ytp-ad-module, .ytp-ad-overlay-container,
      [class*="ad-showing"], [class*="ad-interrupting"],
      ytd-promoted-sparkles-web-renderer,
      ytd-ad-slot-renderer, ytd-promoted-video-renderer {
        display: none !important;
      }
    `;
    
    const styleSheet = document.createElement('style');
    styleSheet.type = 'text/css';
    styleSheet.innerHTML = youtubeCSS;
    styleSheet.id = 'smart-adblocker-youtube-css';
    
    const target = document.head || document.body || document.documentElement;
    if (target) {
      target.appendChild(styleSheet);
    }
    return;
  }
  
  // Diğer siteler için genel CSS
  const adBlockCSS = `
    /* Genel reklam selektörleri - daha konservatif */
    iframe[src*="googlesyndication"],
    iframe[src*="doubleclick"],
    iframe[src*="googleadservices"],
    div[data-ad-client],
    
    /* Açık reklam class'ları */
    .advertisement, .banner-ad, .popup-ad,
    
    /* Cookie banner'ları */
    [class*="cookie-consent"], [class*="cookie-banner"],
    [id*="cookie-consent"], [id*="cookie-banner"],
    
    /* Newsletter popup'ları */
    [class*="newsletter-popup"], [class*="subscribe-popup"],
    [class*="email-popup"] {
      display: none !important;
    }
    
    /* Body scroll'u geri getir */
    body {
      overflow: auto !important;
    }
  `;
  
  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerHTML = adBlockCSS;
  styleSheet.id = 'smart-adblocker-css';
  
  // Head varsa oraya, yoksa body'ye ekle
  const target = document.head || document.body || document.documentElement;
  if (target) {
    target.appendChild(styleSheet);
  }
}

// DOM'dan reklam elementlerini kaldır
function removeAdElements() {
  // YouTube'da DOM manipulation yapma
  if (window.location.hostname.includes('youtube.com')) {
    return;
  }
  
  // Yaygın reklam selektörleri - daha konservatif
  const adSelectors = [
    'iframe[src*="googlesyndication"]',
    'iframe[src*="doubleclick"]',
    'div[data-ad-client]',
    '.advertisement', '.banner-ad',
    '.cookie-consent', '.cookie-banner',
    '.newsletter-popup'
  ];
  
  adSelectors.forEach(selector => {
    try {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        // Önemli content'i silmemek için kontrol
        if (!isImportantElement(element)) {
          element.remove();
        }
      });
    } catch (error) {
      // Selector hatası, devam et
    }
  });
}

// Önemli element kontrolü
function isImportantElement(element) {
  const importantTags = ['nav', 'header', 'main', 'article', 'section'];
  const importantClasses = ['content', 'main', 'primary', 'article', 'post'];
  
  // Tag kontrol
  if (importantTags.includes(element.tagName.toLowerCase())) {
    return true;
  }
  
  // Class kontrol
  const className = element.className.toLowerCase();
  return importantClasses.some(cls => className.includes(cls));
}

// Video reklamlarını blokla
function blockVideoAds() {
  // YouTube için çok konservatif yaklaşım
  if (window.location.hostname.includes('youtube.com')) {
    // Sadece açık reklam container'larını gizle
    const ytAdSelectors = [
      '.video-ads', '.ytp-ad-module', '.ytp-ad-overlay-container'
    ];
    
    ytAdSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(el => el.style.display = 'none');
    });
    return;
  }
  
  // Diğer siteler için video reklam engelleme
  const videoAdSelectors = [
    'video[src*="googlesyndication"]',
    'video[src*="doubleclick"]',
    'video[src*="/ads/"]'
  ];
  
  videoAdSelectors.forEach(selector => {
    const videos = document.querySelectorAll(selector);
    videos.forEach(video => {
      video.style.display = 'none';
    });
  });
}

// Popup'ları blokla
function blockPopups() {
  // Modal ve overlay'leri kaldır
  const popupSelectors = [
    '.modal', '.popup', '.overlay', '.backdrop',
    '[role="dialog"]', '[role="alertdialog"]'
  ];
  
  popupSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      if (element.innerHTML.toLowerCase().includes('ad') || 
          element.innerHTML.toLowerCase().includes('subscribe') ||
          element.innerHTML.toLowerCase().includes('newsletter')) {
        element.remove();
      }
    });
  });
  
  // Body scroll'u geri getir
  document.body.style.overflow = 'auto';
  document.documentElement.style.overflow = 'auto';
}

// DOM değişikliklerini izle
function observeDOM() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Yeni eklenen elementleri kontrol et
            checkNewElement(node);
          }
        });
      }
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

// Yeni element kontrolü
function checkNewElement(element) {
  // Reklam benzeri elementleri kaldır
  const adKeywords = ['ad', 'advertisement', 'banner', 'popup', 'sponsored'];
  const elementClass = (element.className || '').toLowerCase();
  const elementId = (element.id || '').toLowerCase();
  
  if (adKeywords.some(keyword => 
    elementClass.includes(keyword) || elementId.includes(keyword)
  )) {
    if (!isImportantElement(element)) {
      element.remove();
    }
  }
}

// YouTube için özel ad blocking
function startYouTubeAdBlocking() {
  console.log('Starting YouTube-specific ad blocking');
  
  // Sadece YouTube reklamları için CSS
  const youtubeAdCSS = `
    /* YouTube video reklamları */
    .video-ads,
    .ytp-ad-module,
    .ytp-ad-overlay-container,
    .ytp-ad-skip-button-container,
    .ytp-ad-overlay-close-button,
    .ytp-ad-image-overlay,
    
    /* YouTube promoted content */
    ytd-promoted-sparkles-web-renderer,
    ytd-ad-slot-renderer,
    ytd-promoted-video-renderer,
    ytd-compact-promoted-video-renderer,
    ytd-promoted-sparkles-text-search-renderer,
    
    /* Masthead ads */
    ytd-rich-section-renderer[is-masthead-ad],
    
    /* Display ads */
    #player-ads,
    .ytd-display-ad-renderer,
    
    /* Google ads */
    div[id^="google_ads_"],
    
    /* Sidebar ads */
    ytd-rich-item-renderer[is-ad] {
      display: none !important;
      visibility: hidden !important;
      height: 0 !important;
      width: 0 !important;
      opacity: 0 !important;
    }
  `;
  
  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerHTML = youtubeAdCSS;
  styleSheet.id = 'smart-adblocker-youtube';
  
  const target = document.head || document.body || document.documentElement;
  if (target) {
    target.appendChild(styleSheet);
  }
  
  // YouTube reklam elementlerini periyodik olarak kontrol et
  setInterval(removeYouTubeAds, 1000);
  
  // Sayfa yüklendiğinde de kontrol et
  setTimeout(removeYouTubeAds, 2000);
}

// YouTube reklamlarını kaldır
function removeYouTubeAds() {
  // Video reklam container'ları - daha kapsamlı
  const adSelectors = [
    '.video-ads',
    '.ytp-ad-module', 
    '.ytp-ad-overlay-container',
    '.ytp-ad-image-overlay',
    '.ytp-ad-overlay-close-button',
    'ytd-promoted-sparkles-web-renderer',
    'ytd-ad-slot-renderer',
    'ytd-promoted-video-renderer',
    'ytd-compact-promoted-video-renderer',
    'ytd-promoted-sparkles-text-search-renderer',
    '#player-ads',
    '.ytd-display-ad-renderer',
    '.ytd-promoted-video-renderer',
    'div[id^="google_ads_"]',
    // Sidebar ads
    'ytd-rich-item-renderer[is-ad]',
    // Masthead
    'ytd-rich-section-renderer[is-masthead-ad]'
  ];
  
  adSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      if (element) {
        element.style.display = 'none';
        element.remove();
      }
    });
  });
  
  // Skip ad button'a otomatik tıkla
  const skipButtons = document.querySelectorAll('.ytp-ad-skip-button, .ytp-skip-ad-button');
  skipButtons.forEach(button => {
    if (button && button.offsetParent !== null) {
      setTimeout(() => button.click(), 100);
    }
  });
  
  // Video player'da reklam varsa hızlandır
  const video = document.querySelector('video');
  if (video && video.duration) {
    // Eğer video çok kısaysa (reklam olabilir) ve skip button yoksa
    if (video.duration < 30) {
      const hasSkipButton = document.querySelector('.ytp-ad-skip-button, .ytp-skip-ad-button');
      if (!hasSkipButton) {
        video.currentTime = video.duration - 0.1;
        video.playbackRate = 16; // Hızlandır
      }
    }
  }
}

// Background script'ten mesaj geldiğinde
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Content script received message:', request);
  
  switch(request.action) {
    case 'updateSettings':
      initialize();
      sendResponse({ success: true });
      break;
  }
});

// Sayfa unload olduğunda temizlik yap
window.addEventListener('beforeunload', () => {
  const adBlockCSS = document.getElementById('smart-adblocker-css');
  if (adBlockCSS) {
    adBlockCSS.remove();
  }
  const youtubeCSS = document.getElementById('smart-adblocker-youtube');
  if (youtubeCSS) {
    youtubeCSS.remove();
  }
});