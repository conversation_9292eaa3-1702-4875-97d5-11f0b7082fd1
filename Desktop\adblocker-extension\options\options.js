// Smart AdBlocker Options Script
console.log('Smart AdBlocker options page loaded');

let currentSettings = {};

// DOM yüklendiğinde başlat
document.addEventListener('DOMContentLoaded', initialize);

async function initialize() {
  console.log('Initializing options page');
  
  // Ayarları yükle
  await loadSettings();
  
  // UI'ı güncelle
  updateUI();
  
  // Event listener'ları ekle
  setupEventListeners();
}

// Ayarları yükle
async function loadSettings() {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'getSettings' });
    currentSettings = response.settings || getDefaultSettings();
    console.log('Settings loaded:', currentSettings);
  } catch (error) {
    console.error('Error loading settings:', error);
    currentSettings = getDefaultSettings();
  }
}

// Default ayarları döndür
function getDefaultSettings() {
  return {
    isEnabled: true,
    whitelist: [],
    blockAds: true,
    blockVideoAds: true,
    blockPopups: true,
    blockTrackers: true
  };
}

// UI'ı güncelle
function updateUI() {
  // Toggle'ları güncelle
  updateToggles();
  
  // Whitelist'i güncelle
  updateWhitelist();
  
  // İstatistikleri güncelle
  updateStatistics();
}

// Toggle'ları güncelle
function updateToggles() {
  document.getElementById('mainEnabled').checked = currentSettings.isEnabled || false;
  document.getElementById('blockAds').checked = currentSettings.blockAds || false;
  document.getElementById('blockVideoAds').checked = currentSettings.blockVideoAds || false;
  document.getElementById('blockPopups').checked = currentSettings.blockPopups || false;
  document.getElementById('blockTrackers').checked = currentSettings.blockTrackers || false;
}

// Whitelist'i güncelle
function updateWhitelist() {
  const container = document.getElementById('whitelistContainer');
  const whitelist = currentSettings.whitelist || [];
  
  // Container'ı temizle
  container.innerHTML = '';
  
  if (whitelist.length === 0) {
    const emptyMessage = document.createElement('div');
    emptyMessage.style.textAlign = 'center';
    emptyMessage.style.color = '#999';
    emptyMessage.style.padding = '20px';
    emptyMessage.style.fontSize = '14px';
    emptyMessage.textContent = 'No whitelisted sites yet. Add a site above to get started.';
    container.appendChild(emptyMessage);
    return;
  }
  
  // Whitelist item'larını oluştur
  whitelist.forEach(site => {
    const item = createWhitelistItem(site);
    container.appendChild(item);
  });
}

// Whitelist item oluştur
function createWhitelistItem(site) {
  const item = document.createElement('div');
  item.className = 'whitelist-item';
  
  const siteName = document.createElement('span');
  siteName.className = 'site-name';
  siteName.textContent = site;
  
  const removeBtn = document.createElement('button');
  removeBtn.className = 'remove-btn';
  removeBtn.textContent = 'Remove';
  removeBtn.addEventListener('click', () => removeSiteFromWhitelist(site));
  
  item.appendChild(siteName);
  item.appendChild(removeBtn);
  
  return item;
}

// İstatistikleri güncelle
function updateStatistics() {
  // Mock data - gerçek implementasyonda storage'dan gelecek
  document.getElementById('totalAdsBlocked').textContent = '1,247';
  document.getElementById('totalTrackersBlocked').textContent = '892';
  document.getElementById('whitelistedSites').textContent = (currentSettings.whitelist || []).length;
}

// Event listener'ları ekle
function setupEventListeners() {
  // Main settings toggles
  document.getElementById('mainEnabled').addEventListener('change', async (e) => {
    currentSettings.isEnabled = e.target.checked;
    await saveSettings();
  });
  
  document.getElementById('blockAds').addEventListener('change', async (e) => {
    currentSettings.blockAds = e.target.checked;
    await saveSettings();
  });
  
  document.getElementById('blockVideoAds').addEventListener('change', async (e) => {
    currentSettings.blockVideoAds = e.target.checked;
    await saveSettings();
  });
  
  document.getElementById('blockPopups').addEventListener('change', async (e) => {
    currentSettings.blockPopups = e.target.checked;
    await saveSettings();
  });
  
  document.getElementById('blockTrackers').addEventListener('change', async (e) => {
    currentSettings.blockTrackers = e.target.checked;
    await saveSettings();
  });
  
  // Whitelist management
  document.getElementById('addSiteBtn').addEventListener('click', addSiteToWhitelist);
  document.getElementById('newSiteInput').addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      addSiteToWhitelist();
    }
  });
  
  // Reset button
  document.getElementById('resetBtn').addEventListener('click', resetAllSettings);
}

// Site'ı whitelist'e ekle
async function addSiteToWhitelist() {
  const input = document.getElementById('newSiteInput');
  const site = input.value.trim().toLowerCase();
  
  if (!site) {
    showMessage('Please enter a website', 'error');
    return;
  }
  
  // URL'yi temizle (http/https prefix'lerini kaldır)
  const cleanSite = site.replace(/^https?:\/\//, '').replace(/^www\./, '');
  
  // Geçerli domain formatını kontrol et
  if (!isValidDomain(cleanSite)) {
    showMessage('Please enter a valid website (e.g., example.com)', 'error');
    return;
  }
  
  // Zaten whitelist'te mi kontrol et
  if (currentSettings.whitelist && currentSettings.whitelist.includes(cleanSite)) {
    showMessage('This site is already in your whitelist', 'error');
    return;
  }
  
  // Whitelist'e ekle
  if (!currentSettings.whitelist) {
    currentSettings.whitelist = [];
  }
  
  currentSettings.whitelist.push(cleanSite);
  
  // Kaydet ve UI'ı güncelle
  await saveSettings();
  updateWhitelist();
  updateStatistics();
  
  // Input'u temizle
  input.value = '';
  
  showMessage(`Added ${cleanSite} to whitelist`, 'success');
}

// Site'ı whitelist'ten kaldır
async function removeSiteFromWhitelist(site) {
  if (!currentSettings.whitelist) {
    return;
  }
  
  const index = currentSettings.whitelist.indexOf(site);
  if (index > -1) {
    currentSettings.whitelist.splice(index, 1);
    
    // Kaydet ve UI'ı güncelle
    await saveSettings();
    updateWhitelist();
    updateStatistics();
    
    showMessage(`Removed ${site} from whitelist`, 'success');
  }
}

// Domain geçerliliğini kontrol et
function isValidDomain(domain) {
  const domainPattern = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]\.?[a-zA-Z]{2,}$/;
  return domainPattern.test(domain);
}

// Tüm ayarları sıfırla
async function resetAllSettings() {
  const confirmed = confirm('Are you sure you want to reset all settings? This action cannot be undone.');
  
  if (confirmed) {
    currentSettings = getDefaultSettings();
    await saveSettings();
    updateUI();
    showMessage('All settings have been reset to defaults', 'success');
  }
}

// Ayarları kaydet
async function saveSettings() {
  try {
    await chrome.runtime.sendMessage({ 
      action: 'updateSettings', 
      settings: currentSettings 
    });
    console.log('Settings saved:', currentSettings);
    showSaveStatus();
  } catch (error) {
    console.error('Error saving settings:', error);
    showMessage('Error saving settings', 'error');
  }
}

// Kaydetme durumunu göster
function showSaveStatus() {
  const saveStatus = document.getElementById('saveStatus');
  saveStatus.classList.add('show');
  
  setTimeout(() => {
    saveStatus.classList.remove('show');
  }, 2000);
}

// Mesaj göster
function showMessage(message, type = 'info') {
  // Basit alert şimdilik - daha sonra toast notification yapılabilir
  if (type === 'error') {
    alert('Error: ' + message);
  } else {
    console.log(message);
  }
}

// Sayfa kapatılırken ayarları kaydet
window.addEventListener('beforeunload', () => {
  saveSettings();
});

// Error handling
window.addEventListener('error', (e) => {
  console.error('Options page error:', e.error);
});

// Unhandled promise rejections
window.addEventListener('unhandledrejection', (e) => {
  console.error('Unhandled promise rejection:', e.reason);
});

// Export functions for testing (development only)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    loadSettings,
    saveSettings,
    addSiteToWhitelist,
    removeSiteFromWhitelist,
    isValidDomain
  };
}