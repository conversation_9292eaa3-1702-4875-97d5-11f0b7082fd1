// Background Service Worker - Smart AdBlocker
console.log('Smart AdBlocker background script loaded');

// Extension kurulduğunda çalışır
chrome.runtime.onInstalled.addListener(() => {
  console.log('Smart AdBlocker installed');
  
  // Default ayarları yükle
  initializeDefaultSettings();
});

// Default ayarları initialize et
async function initializeDefaultSettings() {
  const defaultSettings = {
    isEnabled: true,
    whitelist: [],
    blockAds: true,
    blockVideoAds: true,
    blockPopups: true,
    blockTrackers: true
  };
  
  // Mevcut ayarları kontrol et
  const result = await chrome.storage.sync.get(['settings']);
  if (!result.settings) {
    // İlk kurulum - default ayarları kaydet
    await chrome.storage.sync.set({ settings: defaultSettings });
    console.log('Default settings initialized');
  }
}

// Popup'tan mesaj geldiğinde
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request);
  
  switch(request.action) {
    case 'toggleSite':
      toggleSiteInWhitelist(request.hostname);
      sendResponse({ success: true });
      break;
      
    case 'getSettings':
      getSettings().then(settings => {
        sendResponse({ settings });
      });
      return true; // Async response için
      
    case 'updateSettings':
      updateSettings(request.settings).then(() => {
        sendResponse({ success: true });
      });
      return true;
      
    case 'checkWhitelist':
      checkWhitelist(request.hostname).then(isWhitelisted => {
        sendResponse({ isWhitelisted });
      });
      return true;
  }
});

// Site'ı whitelist'e ekle/çıkar
async function toggleSiteInWhitelist(hostname) {
  const result = await chrome.storage.sync.get(['settings']);
  const settings = result.settings || {};
  
  if (!settings.whitelist) {
    settings.whitelist = [];
  }
  
  const index = settings.whitelist.indexOf(hostname);
  if (index > -1) {
    // Whitelist'ten çıkar
    settings.whitelist.splice(index, 1);
    console.log(`Removed ${hostname} from whitelist`);
  } else {
    // Whitelist'e ekle
    settings.whitelist.push(hostname);
    console.log(`Added ${hostname} to whitelist`);
  }
  
  await chrome.storage.sync.set({ settings });
  
  // Content script'leri güncelle
  updateContentScripts();
}

// Ayarları getir
async function getSettings() {
  const result = await chrome.storage.sync.get(['settings']);
  return result.settings || {};
}

// Ayarları güncelle
async function updateSettings(newSettings) {
  await chrome.storage.sync.set({ settings: newSettings });
  updateContentScripts();
}

// Whitelist kontrolü
async function checkWhitelist(hostname) {
  const settings = await getSettings();
  return settings.whitelist && settings.whitelist.includes(hostname);
}

// Aktif tab'lardaki content script'leri güncelle
async function updateContentScripts() {
  try {
    const tabs = await chrome.tabs.query({ active: true });
    for (const tab of tabs) {
      if (tab.url && !tab.url.startsWith('chrome://')) {
        chrome.tabs.sendMessage(tab.id, { action: 'updateSettings' }).catch(() => {
          // Tab henüz yüklenmemiş olabilir, hata yok sayılabilir
        });
      }
    }
  } catch (error) {
    console.log('Error updating content scripts:', error);
  }
}

// Tab güncellendiğinde
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'loading' && tab.url && !tab.url.startsWith('chrome://')) {
    // Yeni sayfa yüklenirken ayarları gönder
    setTimeout(() => {
      chrome.tabs.sendMessage(tabId, { action: 'updateSettings' }).catch(() => {
        // Sayfa henüz hazır olmayabilir
      });
    }, 100);
  }
});