// Background Service Worker - Smart AdBlocker
console.log('Smart AdBlocker background script loaded');

// Extension kurulduğunda çalışır
chrome.runtime.onInstalled.addListener(() => {
  console.log('Smart AdBlocker installed');

  // Default ayarları yükle
  initializeDefaultSettings();

  // Network request blocking'i başlat
  setupNetworkBlocking();
});

// Default ayarları initialize et
async function initializeDefaultSettings() {
  const defaultSettings = {
    isEnabled: true,
    whitelist: [],
    blockAds: true,
    blockVideoAds: true,
    blockPopups: true,
    blockTrackers: true
  };
  
  // Mevcut ayarları kontrol et
  const result = await chrome.storage.sync.get(['settings']);
  if (!result.settings) {
    // İlk kurulum - default ayarları kaydet
    await chrome.storage.sync.set({ settings: defaultSettings });
    console.log('Default settings initialized');
  }
}

// Popup'tan mesaj geldiğinde
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request);
  
  switch(request.action) {
    case 'toggleSite':
      toggleSiteInWhitelist(request.hostname);
      sendResponse({ success: true });
      break;
      
    case 'getSettings':
      getSettings().then(settings => {
        sendResponse({ settings });
      });
      return true; // Async response için
      
    case 'updateSettings':
      updateSettings(request.settings).then(() => {
        sendResponse({ success: true });
      });
      return true;
      
    case 'checkWhitelist':
      checkWhitelist(request.hostname).then(isWhitelisted => {
        sendResponse({ isWhitelisted });
      });
      return true;
  }
});

// Site'ı whitelist'e ekle/çıkar
async function toggleSiteInWhitelist(hostname) {
  const result = await chrome.storage.sync.get(['settings']);
  const settings = result.settings || {};
  
  if (!settings.whitelist) {
    settings.whitelist = [];
  }
  
  const index = settings.whitelist.indexOf(hostname);
  if (index > -1) {
    // Whitelist'ten çıkar
    settings.whitelist.splice(index, 1);
    console.log(`Removed ${hostname} from whitelist`);
  } else {
    // Whitelist'e ekle
    settings.whitelist.push(hostname);
    console.log(`Added ${hostname} to whitelist`);
  }
  
  await chrome.storage.sync.set({ settings });
  
  // Content script'leri güncelle
  updateContentScripts();
}

// Ayarları getir
async function getSettings() {
  const result = await chrome.storage.sync.get(['settings']);
  return result.settings || {};
}

// Ayarları güncelle
async function updateSettings(newSettings) {
  await chrome.storage.sync.set({ settings: newSettings });
  updateContentScripts();
}

// Whitelist kontrolü
async function checkWhitelist(hostname) {
  const settings = await getSettings();
  return settings.whitelist && settings.whitelist.includes(hostname);
}

// Aktif tab'lardaki content script'leri güncelle
async function updateContentScripts() {
  try {
    const tabs = await chrome.tabs.query({ active: true });
    for (const tab of tabs) {
      if (tab.url && !tab.url.startsWith('chrome://')) {
        chrome.tabs.sendMessage(tab.id, { action: 'updateSettings' }).catch(() => {
          // Tab henüz yüklenmemiş olabilir, hata yok sayılabilir
        });
      }
    }
  } catch (error) {
    console.log('Error updating content scripts:', error);
  }
}

// Tab güncellendiğinde
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'loading' && tab.url && !tab.url.startsWith('chrome://')) {
    // Yeni sayfa yüklenirken ayarları gönder
    setTimeout(() => {
      chrome.tabs.sendMessage(tabId, { action: 'updateSettings' }).catch(() => {
        // Sayfa henüz hazır olmayabilir
      });
    }, 100);
  }
});

// Network seviyesinde reklam engelleme
function setupNetworkBlocking() {
  // YouTube reklam URL'lerini engelle
  const adUrlPatterns = [
    "*://googleads.g.doubleclick.net/*",
    "*://googlesyndication.com/*",
    "*://googleadservices.com/*",
    "*://google-analytics.com/*",
    "*://doubleclick.net/*",
    "*://googletagmanager.com/*",
    "*://googletagservices.com/*",
    "*://youtube.com/api/stats/ads*",
    "*://youtube.com/ptracking*",
    "*://youtube.com/youtubei/v1/log_event*",
    "*://s.youtube.com/api/stats/qoe*",
    "*://www.youtube.com/api/stats/ads*",
    "*://www.youtube.com/ptracking*",
    "*://www.youtube.com/youtubei/v1/log_event*",
    "*://s.youtube.com/api/stats/watchtime*",
    "*://youtube.com/get_video_info*",
    "*://www.youtube.com/get_video_info*"
  ];

  // Web request'leri engelle
  chrome.webRequest.onBeforeRequest.addListener(
    function(details) {
      console.log('Blocking ad request:', details.url);
      return { cancel: true };
    },
    {
      urls: adUrlPatterns,
      types: ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "other"]
    },
    ["blocking"]
  );

  // YouTube video reklam URL'lerini özel olarak engelle
  chrome.webRequest.onBeforeRequest.addListener(
    function(details) {
      const url = details.url.toLowerCase();

      // YouTube reklam video URL'leri
      if (url.includes('youtube.com') && (
        url.includes('/videoplayback') && (
          url.includes('&ad_type=') ||
          url.includes('&adformat=') ||
          url.includes('&ad_tag=') ||
          url.includes('&adsystem=')
        )
      )) {
        console.log('Blocking YouTube video ad:', details.url);
        return { cancel: true };
      }

      return {};
    },
    {
      urls: ["*://www.youtube.com/*", "*://youtube.com/*", "*://m.youtube.com/*"],
      types: ["media", "xmlhttprequest", "other"]
    },
    ["blocking"]
  );
}