[{"id": 1, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*googlesyndication.com*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}, {"id": 2, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*doubleclick.net*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame", "image"]}}, {"id": 3, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*googleadservices.com*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}, {"id": 4, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*facebook.com/tr*", "resourceTypes": ["script", "xmlhttprequest", "image"]}}, {"id": 5, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*google-analytics.com*", "resourceTypes": ["script", "xmlhttprequest"]}}, {"id": 6, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*googletagmanager.com*", "resourceTypes": ["script", "xmlhttprequest"]}}, {"id": 7, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*amazon-adsystem.com*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}, {"id": 8, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*adsystem.com*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}, {"id": 9, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*outbrain.com*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}, {"id": 10, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*taboola.com*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}, {"id": 11, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*ads.twitter.com*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}, {"id": 12, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*adsystem*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}, {"id": 13, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*/ads/*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}, {"id": 14, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*/advertisements/*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}, {"id": 15, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*popads*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}, {"id": 16, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*popup*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}, {"id": 17, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*banner*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame", "image"]}}, {"id": 18, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*tracking*", "resourceTypes": ["script", "xmlhttprequest", "image"]}}, {"id": 19, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*analytics*", "resourceTypes": ["script", "xmlhttprequest"]}}, {"id": 20, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*youtube.com/api/stats/ads*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 21, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*youtube.com/get_video_info*ad*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 22, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*youtube.com/ptracking*", "resourceTypes": ["xmlhttprequest", "image"]}}, {"id": 23, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*facebook.com/plugins*", "resourceTypes": ["script", "sub_frame"]}}, {"id": 24, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*twitter.com/widgets*", "resourceTypes": ["script", "sub_frame"]}}, {"id": 25, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*linkedin.com/platform*", "resourceTypes": ["script", "sub_frame"]}}, {"id": 26, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*addthis.com*", "resourceTypes": ["script", "xmlhttprequest"]}}, {"id": 27, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*sharethis.com*", "resourceTypes": ["script", "xmlhttprequest"]}}, {"id": 28, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*criteo.com*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}, {"id": 29, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*adsense*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}, {"id": 30, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*adnxs.com*", "resourceTypes": ["script", "xmlhttprequest", "sub_frame"]}}]