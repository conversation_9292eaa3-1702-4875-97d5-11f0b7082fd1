<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart AdBlocker</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="popup-container">
        <!-- Header -->
        <div class="header">
            <h1 class="title">Smart AdBlocker</h1>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot" id="statusDot"></span>
                <span class="status-text" id="statusText">Loading...</span>
            </div>
        </div>

        <!-- Main Toggle -->
        <div class="main-toggle">
            <label class="toggle-switch">
                <input type="checkbox" id="mainToggle">
                <span class="slider"></span>
            </label>
            <span class="toggle-label">AdBlocker Enabled</span>
        </div>

        <!-- Current Site Section -->
        <div class="current-site" id="currentSiteSection">
            <div class="site-info">
                <span class="site-icon">🌐</span>
                <div class="site-details">
                    <div class="site-name" id="siteName">example.com</div>
                    <div class="site-status" id="siteStatus">Ads Blocked</div>
                </div>
            </div>
            
            <button class="whitelist-btn" id="whitelistBtn">
                <span class="btn-icon" id="whitelistIcon">✓</span>
                <span class="btn-text" id="whitelistText">Allow for this site</span>
            </button>
        </div>

        <!-- Quick Stats -->
        <div class="stats-section">
            <div class="stat-item">
                <div class="stat-number" id="adsBlocked">0</div>
                <div class="stat-label">Ads Blocked</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="trackersBlocked">0</div>
                <div class="stat-label">Trackers Blocked</div>
            </div>
        </div>

        <!-- Quick Settings -->
        <div class="quick-settings">
            <div class="setting-item">
                <label class="setting-toggle">
                    <input type="checkbox" id="blockAdsToggle">
                    <span class="setting-slider"></span>
                </label>
                <span class="setting-label">Block Ads</span>
            </div>
            
            <div class="setting-item">
                <label class="setting-toggle">
                    <input type="checkbox" id="blockVideoAdsToggle">
                    <span class="setting-slider"></span>
                </label>
                <span class="setting-label">Block Video Ads</span>
            </div>
            
            <div class="setting-item">
                <label class="setting-toggle">
                    <input type="checkbox" id="blockPopupsToggle">
                    <span class="setting-slider"></span>
                </label>
                <span class="setting-label">Block Popups</span>
            </div>
            
            <div class="setting-item">
                <label class="setting-toggle">
                    <input type="checkbox" id="blockTrackersToggle">
                    <span class="setting-slider"></span>
                </label>
                <span class="setting-label">Block Trackers</span>
            </div>
        </div>

        <!-- Whitelist Section -->
        <div class="whitelist-section">
            <div class="section-header">
                <span class="section-title">Whitelisted Sites</span>
                <span class="whitelist-count" id="whitelistCount">0</span>
            </div>
            
            <div class="whitelist-list" id="whitelistList">
                <!-- Whitelist items will be populated by JS -->
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <button class="settings-btn" id="settingsBtn">
                ⚙️ Advanced Settings
            </button>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>