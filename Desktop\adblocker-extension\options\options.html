<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart AdBlocker - Settings</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-description {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .setting-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .setting-row:last-child {
            border-bottom: none;
        }

        .setting-info {
            flex: 1;
        }

        .setting-label {
            font-weight: 500;
            font-size: 15px;
            color: #333;
            margin-bottom: 5px;
        }

        .setting-desc {
            font-size: 13px;
            color: #666;
        }

        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            border-radius: 30px;
            transition: 0.4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 24px;
            width: 24px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            border-radius: 50%;
            transition: 0.4s;
        }

        input:checked + .slider {
            background-color: #667eea;
        }

        input:checked + .slider:before {
            transform: translateX(30px);
        }

        .whitelist-section {
            background: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
        }

        .whitelist-add {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .whitelist-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .add-btn {
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .add-btn:hover {
            background: #5a6fd8;
        }

        .whitelist-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .whitelist-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            background: white;
            border-radius: 6px;
            margin-bottom: 8px;
            border: 1px solid #eee;
        }

        .whitelist-item:last-child {
            margin-bottom: 0;
        }

        .site-name {
            font-size: 14px;
            color: #333;
        }

        .remove-btn {
            background: #ff4757;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .remove-btn:hover {
            background: #ff3742;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .footer {
            background: #f9f9f9;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #eee;
        }

        .version {
            color: #666;
            font-size: 12px;
        }

        .reset-btn {
            background: #ff4757;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }

        .reset-btn:hover {
            background: #ff3742;
        }

        .save-status {
            color: #4CAF50;
            font-size: 12px;
            margin-left: 10px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .save-status.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ Smart AdBlocker</h1>
            <p>Advanced Settings & Configuration</p>
        </div>

        <div class="content">
            <!-- Main Settings -->
            <div class="section">
                <div class="section-title">
                    ⚙️ General Settings
                </div>
                <div class="section-description">
                    Configure the main functionality of Smart AdBlocker
                </div>

                <div class="setting-row">
                    <div class="setting-info">
                        <div class="setting-label">Enable AdBlocker</div>
                        <div class="setting-desc">Turn on/off ad blocking globally</div>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="mainEnabled">
                        <span class="slider"></span>
                    </label>
                </div>

                <div class="setting-row">
                    <div class="setting-info">
                        <div class="setting-label">Block Advertisements</div>
                        <div class="setting-desc">Block banner ads, display ads, and sponsored content</div>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="blockAds">
                        <span class="slider"></span>
                    </label>
                </div>

                <div class="setting-row">
                    <div class="setting-info">
                        <div class="setting-label">Block Video Advertisements</div>
                        <div class="setting-desc">Block pre-roll, mid-roll, and overlay video ads</div>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="blockVideoAds">
                        <span class="slider"></span>
                    </label>
                </div>

                <div class="setting-row">
                    <div class="setting-info">
                        <div class="setting-label">Block Popups</div>
                        <div class="setting-desc">Block popup windows, modals, and overlay ads</div>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="blockPopups">
                        <span class="slider"></span>
                    </label>
                </div>

                <div class="setting-row">
                    <div class="setting-info">
                        <div class="setting-label">Block Trackers</div>
                        <div class="setting-desc">Block analytics, tracking scripts, and social widgets</div>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="blockTrackers">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>

            <!-- Whitelist Management -->
            <div class="section">
                <div class="section-title">
                    📝 Whitelist Management
                </div>
                <div class="section-description">
                    Manage sites where ads are allowed to show
                </div>

                <div class="whitelist-section">
                    <div class="whitelist-add">
                        <input type="text" class="whitelist-input" id="newSiteInput" 
                               placeholder="Enter website (e.g., example.com)">
                        <button class="add-btn" id="addSiteBtn">Add Site</button>
                    </div>

                    <div class="whitelist-list" id="whitelistContainer">
                        <!-- Whitelist items will be populated by JS -->
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="section">
                <div class="section-title">
                    📊 Statistics
                </div>
                <div class="section-description">
                    Your ad blocking statistics
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalAdsBlocked">0</div>
                        <div class="stat-label">Total Ads Blocked</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalTrackersBlocked">0</div>
                        <div class="stat-label">Trackers Blocked</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="whitelistedSites">0</div>
                        <div class="stat-label">Whitelisted Sites</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <span class="version">Smart AdBlocker v1.0.0</span>
            <button class="reset-btn" id="resetBtn">Reset All Settings</button>
            <span class="save-status" id="saveStatus">✓ Settings saved</span>
        </div>
    </div>

    <script src="options.js"></script>
</body>
</html>