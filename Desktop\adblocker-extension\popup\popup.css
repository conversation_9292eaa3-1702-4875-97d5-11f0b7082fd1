/* Smart AdBlocker Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  width: 320px;
  min-height: 400px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
}

.popup-container {
  background: white;
  border-radius: 12px;
  margin: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

/* Header */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px;
  text-align: center;
}

.title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4CAF50;
}

.status-dot.inactive {
  background: #f44336;
}

.status-text {
  font-size: 12px;
  opacity: 0.9;
}

/* Main Toggle */
.main-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.toggle-switch {
  position: relative;
  width: 50px;
  height: 26px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  border-radius: 26px;
  transition: 0.3s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  border-radius: 50%;
  transition: 0.3s;
}

input:checked + .slider {
  background-color: #667eea;
}

input:checked + .slider:before {
  transform: translateX(24px);
}

.toggle-label {
  font-weight: 500;
  font-size: 14px;
}

/* Current Site */
.current-site {
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.site-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.site-icon {
  font-size: 16px;
}

.site-name {
  font-weight: 500;
  font-size: 14px;
}

.site-status {
  font-size: 12px;
  color: #666;
}

.whitelist-btn {
  width: 100%;
  padding: 10px;
  border: 1px solid #667eea;
  background: white;
  color: #667eea;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s;
}

.whitelist-btn:hover {
  background: #667eea;
  color: white;
}

.whitelist-btn.whitelisted {
  background: #4CAF50;
  border-color: #4CAF50;
  color: white;
}

.whitelist-btn.whitelisted:hover {
  background: #45a049;
}

/* Stats Section */
.stats-section {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
}

.stat-label {
  font-size: 11px;
  color: #666;
  margin-top: 4px;
}

/* Quick Settings */
.quick-settings {
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-toggle {
  position: relative;
  width: 40px;
  height: 20px;
}

.setting-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.setting-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  border-radius: 20px;
  transition: 0.3s;
}

.setting-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  border-radius: 50%;
  transition: 0.3s;
}

input:checked + .setting-slider {
  background-color: #667eea;
}

input:checked + .setting-slider:before {
  transform: translateX(20px);
}

.setting-label {
  font-size: 13px;
  color: #333;
}

/* Whitelist Section */
.whitelist-section {
  padding: 16px;
  max-height: 120px;
  overflow-y: auto;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.section-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.whitelist-count {
  background: #667eea;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
}

.whitelist-list {
  max-height: 80px;
  overflow-y: auto;
}

.whitelist-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.whitelist-item:last-child {
  border-bottom: none;
}

.whitelist-site {
  font-size: 12px;
  color: #333;
  flex: 1;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.remove-btn {
  background: none;
  border: none;
  color: #f44336;
  cursor: pointer;
  font-size: 14px;
  padding: 2px;
  border-radius: 3px;
  transition: background 0.3s;
}

.remove-btn:hover {
  background: #ffebee;
}

/* Footer */
.footer {
  padding: 12px 16px;
  background: #f9f9f9;
}

.settings-btn {
  width: 100%;
  padding: 8px;
  background: none;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
}

.settings-btn:hover {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* Scrollbar */
.whitelist-list::-webkit-scrollbar {
  width: 4px;
}

.whitelist-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.whitelist-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.whitelist-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.popup-container {
  animation: fadeIn 0.3s ease-out;
}